{"cells": [{"cell_type": "markdown", "metadata": {"id": "title"}, "source": ["# 🎯 Bisaya TTS LoRA Training\n", "## Data-Adaptive Transfer Learning: LoRA and Submodular Selection for High-Quality Bisaya Speech Synthesis\n", "\n", "**Thesis by:** [Your Name]  \n", "**Course:** CS4A - 1st Semester  \n", "**Institution:** USTP\n", "\n", "---\n", "\n", "### 📋 Notebook Overview:\n", "1. **Environment Setup** - Install dependencies and mount Drive\n", "2. **Dataset Preparation** - Analyze and prepare Bisaya datasets\n", "3. **Bloom LoRA Training** - Train on natural Bisaya speech\n", "4. **Phoneme LoRA Training** - Train on phonemic pronunciation data\n", "5. **Results Analysis** - Evaluate and compare models\n", "\n", "### 🚀 Quick Start:\n", "- **First time:** Run cells 1-3, then 4 or 5\n", "- **Returning:** Run cell 1, then 4 or 5\n", "- **Just training:** Run cell 4 or 5 only"]}, {"cell_type": "markdown", "metadata": {"id": "setup_header"}, "source": ["## 🔧 1. Environment Setup\n", "**Run this cell once per Colab session**"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "setup_cell"}, "outputs": [], "source": ["# === PURE PYTORCH ENVIRONMENT SETUP ===\n", "print(\"🚀 Setting up Pure PyTorch Bisaya TTS Environment...\")\n", "print(\"=\" * 60)\n", "\n", "# 1. Mount Google Drive\n", "print(\"📁 Mounting Google Drive...\")\n", "from google.colab import drive\n", "drive.mount('/content/drive')\n", "\n", "# 2. Change to project directory\n", "import os\n", "project_dir = '/content/drive/MyDrive/Thesis_CS4A'\n", "os.chdir(project_dir)\n", "print(f\"📂 Working directory: {os.getcwd()}\")\n", "\n", "# 3. Install ONLY essential packages (no TTS library!)\n", "print(\"\\n🐍 Installing essential packages...\")\n", "!pip install torch torchaudio librosa soundfile numpy pandas matplotlib tqdm\n", "\n", "# 4. Verify installation\n", "print(\"\\n🔍 Verifying installation...\")\n", "try:\n", "    import torch\n", "    import torchaudio\n", "    import librosa\n", "    import soundfile\n", "    import numpy as np\n", "    import pandas as pd\n", "    \n", "    print(f\"✅ PyTorch version: {torch.__version__}\")\n", "    print(f\"✅ TorchAudio version: {torchaudio.__version__}\")\n", "    print(f\"✅ Librosa version: {librosa.__version__}\")\n", "    print(f\"✅ CUDA available: {torch.cuda.is_available()}\")\n", "    \n", "    if torch.cuda.is_available():\n", "        print(f\"🚀 GPU: {torch.cuda.get_device_name(0)}\")\n", "        print(f\"💾 GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB\")\n", "    else:\n", "        print(\"⚠️ No GPU available - training will be slower\")\n", "        \n", "    print(\"\\n✅ All core libraries working!\")\n", "    \n", "except ImportError as e:\n", "    print(f\"❌ Import error: {e}\")\n", "\n", "# 5. Check project structure\n", "print(\"\\n📋 Checking required files...\")\n", "required_files = [\n", "    'pretrained_models/ljspeech_vits_pretrained.pth',\n", "    'pretrained_models/vits_config.json',\n", "    'bloom_ceb_dataset/train/metadata_ljspeech.csv',\n", "    'pytorch_vits_lora.py',\n", "    'train_bisaya_lora.py'\n", "]\n", "\n", "all_good = True\n", "for file in required_files:\n", "    if os.path.exists(file):\n", "        size = os.path.getsize(file) / (1024*1024)  # MB\n", "        print(f\"✅ {file} ({size:.1f} MB)\")\n", "    else:\n", "        print(f\"❌ {file} - MISSING\")\n", "        all_good = False\n", "\n", "if all_good:\n", "    print(\"\\n🎉 Pure PyTorch environment ready!\")\n", "    print(\"📝 No TTS library dependency issues!\")\n", "    print(\"🚀 Ready for LoRA training!\")\n", "else:\n", "    print(\"\\n⚠️ Some files are missing - check your project structure\")"]}, {"cell_type": "markdown", "metadata": {"id": "dataset_header"}, "source": ["## 📊 2. Dataset Preparation\n", "**Run this once to analyze and prepare your datasets**"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "dataset_analysis"}, "outputs": [], "source": ["# === ANALYZE BISAYA CHARACTER SET ===\n", "print(\"🔍 Analyzing Bisaya character usage...\")\n", "print(\"=\" * 40)\n", "\n", "try:\n", "    exec(open('analyze_bisaya_characters.py').read())\n", "except FileNotFoundError:\n", "    print(\"⚠️ analyze_bisaya_characters.py not found\")\n", "    print(\"📝 Analyzing manually...\")\n", "    \n", "    # Manual analysis\n", "    metadata_file = \"bloom_ceb_dataset/train/metadata_ljspeech.csv\"\n", "    if os.path.exists(metadata_file):\n", "        with open(metadata_file, 'r', encoding='utf-8') as f:\n", "            sample_lines = f.readlines()[:10]\n", "        \n", "        print(f\"📄 Found {len(sample_lines)} sample entries:\")\n", "        for i, line in enumerate(sample_lines, 1):\n", "            parts = line.strip().split('|')\n", "            if len(parts) >= 2:\n", "                print(f\"   {i}. {parts[1]}\")\n", "    else:\n", "        print(f\"❌ Metadata file not found: {metadata_file}\")\n", "\n", "print(\"\\n✅ Character analysis complete!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "dataset_preparation"}, "outputs": [], "source": ["# === PREPARE PHONEME DATASET ===\n", "print(\"🔤 Preparing phoneme dataset...\")\n", "print(\"=\" * 40)\n", "\n", "try:\n", "    exec(open('prepare_phoneme_dataset.py').read())\n", "except FileNotFoundError:\n", "    print(\"⚠️ prepare_phoneme_dataset.py not found\")\n", "    print(\"📝 Creating phoneme dataset manually...\")\n", "    \n", "    # Manual phoneme dataset creation\n", "    import shutil\n", "    \n", "    source_dir = \"Cebuano-Phonemic-TTS-master/data/speech_database\"\n", "    output_dir = \"phoneme_dataset\"\n", "    output_wavs = os.path.join(output_dir, \"wavs\")\n", "    \n", "    if os.path.exists(source_dir):\n", "        os.makedirs(output_wavs, exist_ok=True)\n", "        \n", "        wav_files = [f for f in os.listdir(source_dir) if f.endswith('.wav')]\n", "        print(f\"📁 Found {len(wav_files)} syllable files\")\n", "        \n", "        # Copy first 100 files as sample\n", "        metadata_lines = []\n", "        for i, wav_file in enumerate(wav_files[:100]):\n", "            src = os.path.join(source_dir, wav_file)\n", "            dst = os.path.join(output_wavs, wav_file)\n", "            shutil.copy2(src, dst)\n", "            \n", "            syllable = wav_file.replace('.wav', '')\n", "            metadata_lines.append(f\"{syllable}|{syllable}|{syllable}\")\n", "        \n", "        # Write metadata\n", "        with open(os.path.join(output_dir, \"metadata_ljspeech.csv\"), 'w') as f:\n", "            f.write('\\n'.join(metadata_lines))\n", "        \n", "        print(f\"✅ Created phoneme dataset with {len(metadata_lines)} samples\")\n", "    else:\n", "        print(f\"❌ Phoneme source directory not found: {source_dir}\")\n", "\n", "print(\"\\n✅ Dataset preparation complete!\")"]}, {"cell_type": "markdown", "metadata": {"id": "training_header"}, "source": ["## 🎯 3. Training Options\n", "**Choose one of the training options below**"]}, {"cell_type": "markdown", "metadata": {"id": "bloom_header"}, "source": ["### 🗣️ Option A: <PERSON> (Voice Characteristics)\n", "**Trains on natural Bisaya speech for voice adaptation**"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "bloom_training"}, "outputs": [], "source": ["# === BLOOM LORA TRAINING (PURE PYTORCH) ===\n", "print(\"🎯 Starting Bloom LoRA Training with Pure PyTorch...\")\n", "print(\"=\" * 50)\n", "print(\"📝 This trains on natural Bisaya speech for voice characteristics\")\n", "print(\"⏱️ Expected time: 1.5-2.5 hours (25 epochs) on T4 GPU\")\n", "print(\"🔧 Using pure PyTorch - no TTS library dependencies!\")\n", "print(\"\\n🚀 Starting training...\")\n", "\n", "# Import our custom training module\n", "from train_bisaya_lora import train_bloom_lora\n", "\n", "try:\n", "    # Train with optimized parameters for Colab\n", "    trainer, losses = train_bloom_lora(\n", "        epochs=25,    # Reduced for Colab time limits\n", "        rank=8,       # Good balance of performance vs parameters\n", "        alpha=16.0    # Standard LoRA scaling\n", "    )\n", "    \n", "    print(\"\\n📊 Training Results:\")\n", "    print(f\"   Final loss: {losses[-1]:.4f}\")\n", "    print(f\"   Best loss: {min(losses):.4f}\")\n", "    print(f\"   Total epochs: {len(losses)}\")\n", "    \n", "    # Plot training curve\n", "    import matplotlib.pyplot as plt\n", "    plt.figure(figsize=(10, 6))\n", "    plt.plot(losses)\n", "    plt.title('Bloom LoRA Training Loss')\n", "    plt.xlabel('Epoch')\n", "    plt.ylabel('Loss')\n", "    plt.grid(True)\n", "    plt.show()\n", "    \n", "    print(\"\\n✅ Bloom LoRA training completed successfully!\")\n", "    print(\"📁 Results saved to: training_output/bloom_lora/\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ Training failed: {e}\")\n", "    print(\"🔧 Try the troubleshooting cell below\")"]}, {"cell_type": "markdown", "metadata": {"id": "phoneme_header"}, "source": ["### 🔤 Option B: <PERSON>me LoRA Training (Pronunciation)\n", "**Trains on phonemic data for pronunciation accuracy**"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "phoneme_training"}, "outputs": [], "source": ["# === PHONEME LORA TRAINING ===\n", "print(\"🔤 Starting Phoneme LoRA Training...\")\n", "print(\"=\" * 40)\n", "print(\"📝 This trains on phonemic syllables for pronunciation accuracy\")\n", "print(\"⏱️ Expected time: 20-40 minutes depending on GPU\")\n", "print(\"\\n🚀 Starting training...\")\n", "\n", "try:\n", "    exec(open('train_phoneme_lora.py').read())\n", "except FileNotFoundError:\n", "    print(\"⚠️ train_phoneme_lora.py not found, running manually...\")\n", "    \n", "    # Manual training command\n", "    !python train_vits_lora.py \\\n", "        --dataset_path phoneme_dataset \\\n", "        --description \"Phoneme LoRA - Pronunciation\" \\\n", "        --r 8 \\\n", "        --pretrained_model_path pretrained_models/ljspeech_vits_pretrained.pth\n", "\n", "print(\"\\n✅ Phoneme LoRA training complete!\")\n", "print(\"📁 Check 'training/bisaya_vits_lora/' for outputs\")"]}, {"cell_type": "markdown", "metadata": {"id": "results_header"}, "source": ["## 📊 4. Training Results & Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "check_results"}, "outputs": [], "source": ["# === CHECK TRAINING RESULTS ===\n", "print(\"📊 Checking training results...\")\n", "print(\"=\" * 40)\n", "\n", "# Check training directory\n", "training_dir = \"training/bisaya_vits_lora\"\n", "if os.path.exists(training_dir):\n", "    print(f\"📁 Training directory: {training_dir}\")\n", "    \n", "    # List LoRA adapter files\n", "    lora_files = [f for f in os.listdir(training_dir) if f.startswith('lora_adapters_') and f.endswith('.pth')]\n", "    print(f\"\\n🎯 LoRA adapter files ({len(lora_files)}):\")\n", "    for f in sorted(lora_files):\n", "        size = os.path.getsize(os.path.join(training_dir, f)) / (1024*1024)  # MB\n", "        print(f\"   📄 {f} ({size:.1f} MB)\")\n", "    \n", "    # Check training log\n", "    log_file = os.path.join(training_dir, \"lora_versions.csv\")\n", "    if os.path.exists(log_file):\n", "        print(f\"\\n📈 Training log:\")\n", "        !tail -5 {log_file}\n", "    \n", "    # Show directory contents\n", "    print(f\"\\n📋 All files in training directory:\")\n", "    !ls -la {training_dir}\n", "    \n", "else:\n", "    print(f\"❌ Training directory not found: {training_dir}\")\n", "    print(\"🔄 Make sure you've run training first\")\n", "\n", "print(\"\\n✅ Results check complete!\")"]}, {"cell_type": "markdown", "metadata": {"id": "utils_header"}, "source": ["## 🛠️ 5. Utilities & Troubleshooting"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "quick_setup"}, "outputs": [], "source": ["# === QUICK SETUP (for returning users) ===\n", "print(\"⚡ Quick setup for returning users...\")\n", "\n", "# Mount drive and change directory\n", "from google.colab import drive\n", "drive.mount('/content/drive')\n", "import os\n", "os.chdir('/content/drive/MyDrive/Thesis_CS4A')\n", "\n", "# Check if packages are installed\n", "try:\n", "    import TTS\n", "    import torch\n", "    print(\"✅ Packages already installed\")\n", "    print(f\"✅ TTS: {TTS.__version__}, PyTorch: {torch.__version__}\")\n", "    print(\"🚀 Ready for training!\")\n", "except ImportError:\n", "    print(\"📦 Need to install packages - run the full setup cell first\")\n", "\n", "print(f\"📂 Current directory: {os.getcwd()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "troubleshooting"}, "outputs": [], "source": ["# === TROUBLESHOOTING TOOLS ===\n", "print(\"🔧 Troubleshooting tools...\")\n", "print(\"=\" * 30)\n", "\n", "# Check GPU memory\n", "import torch\n", "if torch.cuda.is_available():\n", "    print(f\"🚀 GPU: {torch.cuda.get_device_name(0)}\")\n", "    print(f\"💾 GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB\")\n", "    print(f\"📊 Memory allocated: {torch.cuda.memory_allocated() / 1e9:.2f} GB\")\n", "    print(f\"📊 Memory cached: {torch.cuda.memory_reserved() / 1e9:.2f} GB\")\n", "else:\n", "    print(\"⚠️ No GPU available\")\n", "\n", "# Check disk space\n", "print(\"\\n💽 Disk space:\")\n", "!df -h /content/drive/MyDrive\n", "\n", "# Check key files\n", "print(\"\\n📋 Key files check:\")\n", "key_files = [\n", "    \"pretrained_models/ljspeech_vits_pretrained.pth\",\n", "    \"pretrained_models/vits_config.json\",\n", "    \"bloom_ceb_dataset/train/metadata_ljspeech.csv\",\n", "    \"train_vits_lora.py\",\n", "    \"requirements.txt\"\n", "]\n", "\n", "for file in key_files:\n", "    if os.path.exists(file):\n", "        size = os.path.getsize(file) / (1024*1024)  # MB\n", "        print(f\"   ✅ {file} ({size:.1f} MB)\")\n", "    else:\n", "        print(f\"   ❌ {file} - MISSING\")\n", "\n", "print(\"\\n🔧 Troubleshooting complete!\")"]}, {"cell_type": "markdown", "metadata": {"id": "footer"}, "source": ["---\n", "\n", "## 📝 Notes & Next Steps\n", "\n", "### 🎯 Training Outputs:\n", "- **LoRA adapters**: `training/bisaya_vits_lora/lora_adapters_TIMESTAMP.pth`\n", "- **Training logs**: `training/bisaya_vits_lora/lora_versions.csv`\n", "- **Checkpoints**: `training/bisaya_vits_lora/`\n", "\n", "### 📊 For Your Thesis:\n", "1. **Compare models**: Bloom LoRA vs Phoneme LoRA vs Base model\n", "2. **Evaluate metrics**: MCD, Mel-spectrogram loss, listening tests\n", "3. **Analyze results**: Which approach works better for Bisaya?\n", "\n", "### 🚀 Advanced Experiments:\n", "- Try different LoRA ranks (r=4, 8, 16)\n", "- Experiment with learning rates\n", "- Combine both datasets\n", "- Test submodular selection\n", "\n", "### 📞 Support:\n", "If you encounter issues, check the troubleshooting cell or refer to your thesis plan document.\n", "\n", "**Good luck with your thesis! 🎓**"]}], "metadata": {"colab": {"provenance": [], "gpuType": "T4"}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}, "accelerator": "GPU"}, "nbformat": 4, "nbformat_minor": 0}