
Bisaya LoRA training package
===========================

Files in this package:
- train_vits_lora.py        : Main trainer script. Accepts --dataset_path, --description, --r, --pretrained_model_path
- train_bloom_lora.py       : Wrapper to train Bloom LoRA (edit paths if needed)
- train_phoneme_lora.py     : Wrapper to train Phoneme LoRA (edit paths if needed)
- requirements.txt          : Recommended pip install packages
- lora_training_notebook.ipynb : Colab notebook with cells to run preparation, training, and inference (open in Colab)
- README.txt                : This file

Quick start (in Colab):
1. Mount Drive and copy this package into /content/ or run from Drive.
2. Install requirements: pip install -r requirements.txt
3. Put your pretrained model and vits_config.json in /content/drive/MyDrive/models/
   - ljspeech_vits_pretrained.pth
   - vits_config.json
4. Prepare datasets in LJSpeech format:
   - /content/drive/MyDrive/BISAYA/bloom_ljspeech/ with wavs/ and metadata_ljspeech.csv
   - /content/drive/MyDrive/BISAYA/phoneme_ljspeech/ with wavs/ and metadata_ljspeech.csv (use phonemes in text fields)
5. Run the wrappers or call train_vits_lora.py directly with proper args.
