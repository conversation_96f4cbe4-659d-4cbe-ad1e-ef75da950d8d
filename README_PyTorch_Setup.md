# 🎯 Pure PyTorch VITS LoRA for Bisaya TTS

**Data-Adaptive Transfer Learning: LoRA and Submodular Selection for High-Quality Bisaya Speech Synthesis**

This implementation provides a **pure PyTorch solution** for VITS + LoRA training, eliminating TTS library dependency issues while maintaining full research capabilities.

## 🚀 Key Advantages

### ✅ **No Dependency Hell**
- Works with **Python 3.12+** (no TTS library conflicts)
- Only requires **PyTorch + basic audio libraries**
- **Guaranteed compatibility** across environments

### 🎓 **Better for Thesis**
- **Deeper technical understanding** - you implement everything
- **More impressive academically** - shows mastery of concepts
- **Full control** over training process
- **Custom LoRA implementation** for VITS

### 🔧 **Technical Benefits**
- **Faster setup** - no complex installations
- **Better debugging** - clear, understandable code
- **Easier customization** - modify anything you want
- **Future-proof** - won't break with updates

## 📁 File Structure

```
Thesis_CS4A/
├── pytorch_vits_lora.py          # Core implementation
├── train_bisaya_lora.py          # Training scripts
├── Bisaya_TTS_LoRA_Training.ipynb # Updated notebook
├── requirements_pytorch.txt       # Minimal requirements
├── pretrained_models/
│   ├── ljspeech_vits_pretrained.pth
│   └── vits_config.json
├── bloom_ceb_dataset/
│   └── train/
│       ├── metadata_ljspeech.csv
│       └── wavs/
└── training_output/               # Results will be saved here
    ├── bloom_lora/
    └── phoneme_lora/
```

## 🚀 Quick Start

### **Option 1: Google Colab (Recommended)**

1. **Upload files to Google Drive:**
   ```
   /content/drive/MyDrive/Thesis_CS4A/
   ```

2. **Open the updated notebook:**
   ```
   Bisaya_TTS_LoRA_Training.ipynb
   ```

3. **Run cells in order:**
   - Cell 1: Environment setup
   - Cell 4: Bloom LoRA training

### **Option 2: Command Line**

```bash
# Install dependencies
pip install -r requirements_pytorch.txt

# Train Bloom LoRA
python train_bisaya_lora.py --mode bloom --epochs 25

# Train Phoneme LoRA
python train_bisaya_lora.py --mode phoneme --epochs 20

# Quick test (3 epochs)
python train_bisaya_lora.py --mode test
```

## 📊 Training Options

### **🎯 Bloom LoRA (Voice Characteristics)**
```python
from train_bisaya_lora import train_bloom_lora

trainer, losses = train_bloom_lora(
    epochs=25,    # Colab-friendly
    rank=8,       # Good balance
    alpha=16.0    # Standard scaling
)
```

### **🔤 Phoneme LoRA (Pronunciation)**
```python
from train_bisaya_lora import train_phoneme_lora

trainer, losses = train_phoneme_lora(
    epochs=20,    # Faster convergence
    rank=8,       # Same as above
    alpha=16.0    # Consistent scaling
)
```

### **🧪 Quick Test**
```python
from train_bisaya_lora import quick_test_training

trainer, losses = quick_test_training(epochs=3)
```

## ⚙️ Configuration

### **LoRA Parameters**
- **rank=4**: Fewer parameters, faster training
- **rank=8**: Balanced performance (recommended)
- **rank=16**: More parameters, potentially better quality

### **Training Parameters**
- **epochs=25**: Good for Bloom dataset
- **epochs=20**: Sufficient for phoneme dataset
- **batch_size=8**: Optimized for Colab T4 GPU

## 📊 Expected Results

### **Training Times (T4 GPU)**
- **Bloom LoRA (25 epochs)**: 1.5-2.5 hours
- **Phoneme LoRA (20 epochs)**: 2-3 hours
- **Quick test (3 epochs)**: 15-30 minutes

### **Output Files**
```
training_output/bloom_lora/
├── lora_adapters_epoch_005.pth
├── lora_adapters_epoch_010.pth
├── ...
├── best_lora_adapters.pth
└── training_history.json
```

## 🔧 Core Components

### **1. LoRA Implementation**
```python
class LoRALayer(nn.Module):
    """Low-Rank Adaptation for parameter-efficient fine-tuning"""
    
class LoRALinear(nn.Module):
    """Linear layer with LoRA adaptation"""
```

### **2. Dataset Handling**
```python
class BisayaDataset(Dataset):
    """Custom dataset for Bisaya TTS training"""
```

### **3. Training Pipeline**
```python
class VITSLoRATrainer:
    """Main trainer with LoRA injection and optimization"""
```

## 🎓 For Your Thesis

### **What You Can Highlight**
- **Custom PyTorch implementation** of VITS + LoRA
- **Parameter-efficient fine-tuning** for low-resource languages
- **Novel adaptation** of LoRA to TTS domain
- **Comprehensive evaluation** of different LoRA configurations

### **Technical Contributions**
- Pure PyTorch VITS training pipeline
- LoRA injection for attention layers
- Bisaya-specific text processing
- Efficient training for resource-constrained environments

## 🔍 Troubleshooting

### **Common Issues**

1. **CUDA out of memory**
   ```python
   # Reduce batch size in pytorch_vits_lora.py
   batch_size=4  # Instead of 8
   ```

2. **Audio loading errors**
   ```python
   # Check audio file paths and formats
   # Ensure all .wav files are in correct directory
   ```

3. **Training too slow**
   ```python
   # Reduce epochs or use smaller rank
   train_bloom_lora(epochs=10, rank=4)
   ```

## 📈 Monitoring Training

### **Real-time Progress**
- Progress bars show current loss
- Training history saved to JSON
- Automatic plotting of loss curves

### **Checkpoints**
- Models saved every 5 epochs
- Best model automatically saved
- Easy to resume interrupted training

## 🎉 Success Indicators

### **Training is Working When:**
- ✅ Loss decreases over epochs
- ✅ No CUDA memory errors
- ✅ LoRA adapters save successfully
- ✅ Training completes without crashes

### **Good Results:**
- Loss converges to stable value
- Training curve shows smooth decrease
- Model generates reasonable outputs

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section
2. Verify all required files exist
3. Ensure GPU memory is sufficient
4. Try reducing batch size or epochs

**Your thesis approach is now fully implemented and ready to run! 🎓**
