from google.colab import drive
drive.mount('/content/drive')
# create workspace

%cd /content/drive/MyDrive/Thesis_CS4A


%cd /content/drive/MyDrive/Thesis_CS4A

# install (<PERSON><PERSON> usually has torch; pip will handle)
!pip install -r requirements.txt


import os, shutil, pandas as pd

DRIVE_BASE = "/content/drive/MyDrive/BISAYA/bloom"  # adjust if needed
OUT = "/content/drive/MyDrive/BISAYA/bloom_ljspeech"
os.makedirs(os.path.join(OUT, "wavs"), exist_ok=True)

def copy_wavs(src_folder, out_wavs):
    for f in os.listdir(src_folder):
        if f.lower().endswith(".wav"):
            src = os.path.join(src_folder, f)
            dst = os.path.join(out_wavs, f)
            if not os.path.exists(dst):
                shutil.copy2(src, dst)

# copy train/val/test wavs
copy_wavs(os.path.join(DRIVE_BASE, "train"), os.path.join(OUT, "wavs"))
copy_wavs(os.path.join(DRIVE_BASE, "validation"), os.path.join(OUT, "wavs"))
copy_wavs(os.path.join(DRIVE_BASE, "test"), os.path.join(OUT, "wavs"))

# Convert train transcripts.txt -> metadata_ljspeech.csv
transcripts_path = os.path.join(DRIVE_BASE, "train", "transcripts.txt")
out_meta = os.path.join(OUT, "metadata_ljspeech.csv")
lines = []
with open(transcripts_path, "r", encoding="utf-8") as f:
    for line in f:
        parts = line.strip().split("\t")
        if len(parts) >= 2:
            fname = parts[0].replace(".wav","")
            text = parts[1].strip()
            # For normalized text we use the same; you can add normalization steps here
            lines.append(f"{fname}|{text}|{text}")

with open(out_meta, "w", encoding="utf-8") as f:
    f.write("\n".join(lines))

print("Created", out_meta, "with", len(lines), "lines")


!python dataset_preparator.py

# clone phoneme repo (you gave this earlier)
!git clone https://github.com/JohnJosephPableo/Cebuano-Phonemic-TTS.git /content/phoneme_repo

# Create phoneme ljspeech folder on Drive
!mkdir -p /content/drive/MyDrive/BISAYA/phoneme_ljspeech/wavs
!cp /content/phoneme_repo/wavs/*.wav /content/drive/MyDrive/BISAYA/phoneme_ljspeech/wavs/

# Convert the repo csv to metadata_ljspeech.csv (adjust filename if different)
python - <<'PY'
import pandas as pd, os
csv_path = "/content/phoneme_repo/cebuano.csv"  # check path
out_meta = "/content/drive/MyDrive/BISAYA/phoneme_ljspeech/metadata_ljspeech.csv"
df = pd.read_csv(csv_path)
lines = []
for _, row in df.iterrows():
    fname = row['filename']
    stem = fname[:-4] if fname.endswith(".wav") else fname
    phon = row.get('phonemes', row.get('phonemes'))  # adjust if header name differs
    lines.append(f"{stem}|{phon}|{phon}")
open(out_meta, "w", encoding="utf-8").write("\n".join(lines))
print("Created", out_meta)
PY


# example: train on bloom_ljspeech
!python /content/bisaya_workspace/train_vits_lora.py \
  --dataset_path "/content/drive/MyDrive/BISAYA/bloom_ljspeech" \
  --description "Bloom LoRA" \
  --r 8 \
  --pretrained_model_path "/content/drive/MyDrive/models/ljspeech_vits_pretrained.pth"


!python /content/bisaya_workspace/train_vits_lora.py \
  --dataset_path "/content/drive/MyDrive/BISAYA/phoneme_ljspeech" \
  --description "Phoneme LoRA" \
  --r 8 \
  --pretrained_model_path "/content/drive/MyDrive/models/ljspeech_vits_pretrained.pth"


import torch
# import your model init utilities (AudioProcessor, TTSTokenizer, Vits, etc.)
from TTS.tts.models.vits import Vits
# Recreate config/ap/tokenizer exactly like training
# ... load vits_config.json and initialize AudioProcessor & tokenizer as in training script ...

# Example pseudo-code:
config_path = "/content/drive/MyDrive/models/vits_config.json"
# load config and init ap/tokenizer similar to train script
# model = Vits(config, ap, tokenizer)
# inject_lora_into_vits(model, r=8)

# load lora adapter
lora_state = torch.load("/content/drive/MyDrive/models/lora_adapters_YYYYMMDD_HHMMSS.pth", map_location='cpu')

# apply weights to LoRA modules
for name, module in model.named_modules():
    # module must be instance of the LoRA wrapper you used
    if hasattr(module, "lora_A") and hasattr(module, "lora_B"):
        module.lora_A.weight.data.copy_(lora_state[f"{name}.lora_A.weight"])
        module.lora_B.weight.data.copy_(lora_state[f"{name}.lora_B.weight"])

# put model on GPU if available
model = model.cuda() if torch.cuda.is_available() else model

# synthesize
text = "Maayong buntag"
wav = model.tts(text)  # adapt to your VITS API
# save wav to file with soundfile or librosa
