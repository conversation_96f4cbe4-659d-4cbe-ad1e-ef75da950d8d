#!/usr/bin/env python3
"""
Analyze the character set used in Bisaya text to configure the VITS model properly.
"""

import os
import pandas as pd
from collections import Counter

def analyze_characters():
    """
    Analyze characters in the Bisaya dataset to create proper character config
    """
    metadata_file = "bloom_ceb_dataset/train/metadata_ljspeech.csv"
    
    if not os.path.exists(metadata_file):
        print(f"❌ Metadata file not found: {metadata_file}")
        return
    
    print("🔍 Analyzing Bisaya character usage...")
    
    # Read all text from metadata
    all_text = ""
    with open(metadata_file, 'r', encoding='utf-8') as f:
        for line in f:
            parts = line.strip().split('|')
            if len(parts) >= 2:
                text = parts[1]  # Get the text part
                all_text += text + " "
    
    # Count character frequency
    char_counter = Counter(all_text)
    
    # Separate into categories
    letters = set()
    punctuation = set()
    numbers = set()
    special = set()
    
    for char, count in char_counter.items():
        if char.isalpha():
            letters.add(char)
        elif char in ".,!?;:\"'()[]{}":
            punctuation.add(char)
        elif char.isdigit():
            numbers.add(char)
        elif char != ' ':  # Skip spaces
            special.add(char)
    
    # Print analysis
    print(f"\n📊 Character Analysis Results:")
    print(f"   📝 Total unique characters: {len(char_counter)}")
    print(f"   🔤 Letters: {len(letters)}")
    print(f"   📍 Punctuation: {len(punctuation)}")
    print(f"   🔢 Numbers: {len(numbers)}")
    print(f"   ⭐ Special: {len(special)}")
    
    print(f"\n🔤 Letters found:")
    print(f"   {''.join(sorted(letters))}")
    
    print(f"\n📍 Punctuation found:")
    print(f"   {''.join(sorted(punctuation))}")
    
    if numbers:
        print(f"\n🔢 Numbers found:")
        print(f"   {''.join(sorted(numbers))}")
    
    if special:
        print(f"\n⭐ Special characters found:")
        print(f"   {''.join(sorted(special))}")
    
    # Generate character config
    all_letters = ''.join(sorted(letters))
    all_punctuation = ''.join(sorted(punctuation))
    
    print(f"\n⚙️  Recommended CharactersConfig:")
    print(f'characters="{all_letters}",')
    print(f'punctuations="{all_punctuation}",')
    
    # Check for common Bisaya characters
    bisaya_chars = set("ñáéíóúàèìòù")
    found_bisaya = bisaya_chars.intersection(letters)
    missing_bisaya = bisaya_chars - letters
    
    if found_bisaya:
        print(f"\n✅ Found Bisaya-specific characters: {''.join(sorted(found_bisaya))}")
    if missing_bisaya:
        print(f"\n⚠️  Missing common Bisaya characters: {''.join(sorted(missing_bisaya))}")
        print("   Consider adding these to your character set if needed.")
    
    # Sample text analysis
    print(f"\n📋 Sample texts:")
    with open(metadata_file, 'r', encoding='utf-8') as f:
        for i, line in enumerate(f):
            if i >= 5:
                break
            parts = line.strip().split('|')
            if len(parts) >= 2:
                print(f"   {i+1}. {parts[1]}")
    
    return all_letters, all_punctuation

if __name__ == "__main__":
    print("🚀 Analyzing Bisaya Character Set")
    print("=" * 50)
    analyze_characters()
