#!/usr/bin/env python3
"""
Complete setup script for running Bisaya LoRA training in Google Colab.
This script handles all the preparation steps needed before training.
"""

import os
import shutil
import subprocess
import sys

def setup_colab_environment():
    """
    Set up the Colab environment for Bisaya TTS training
    """
    print("🚀 Setting up Colab Environment for Bisaya TTS")
    print("=" * 60)
    
    # Step 1: Mount Google Drive
    print("📁 Mounting Google Drive...")
    try:
        from google.colab import drive
        drive.mount('/content/drive')
        print("✅ Google Drive mounted successfully")
    except ImportError:
        print("⚠️  Not running in Colab - skipping drive mount")
    
    # Step 2: Change to project directory
    project_dir = "/content/drive/MyDrive/Thesis_CS4A"
    if os.path.exists(project_dir):
        os.chdir(project_dir)
        print(f"✅ Changed to project directory: {project_dir}")
    else:
        print(f"❌ Project directory not found: {project_dir}")
        return False
    
    # Step 3: Install requirements
    print("\n📦 Installing requirements...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True, capture_output=True)
        print("✅ Requirements installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        return False
    
    # Step 4: Verify pretrained model
    model_path = "pretrained_models/ljspeech_vits_pretrained.pth"
    config_path = "pretrained_models/vits_config.json"
    
    if os.path.exists(model_path) and os.path.exists(config_path):
        print("✅ Pretrained model and config found")
    else:
        print("❌ Pretrained model or config missing")
        print(f"   Expected: {model_path}")
        print(f"   Expected: {config_path}")
        return False
    
    # Step 5: Verify Bloom dataset
    bloom_metadata = "bloom_ceb_dataset/train/metadata_ljspeech.csv"
    bloom_wavs = "bloom_ceb_dataset/train/wavs"
    
    if os.path.exists(bloom_metadata) and os.path.exists(bloom_wavs):
        # Count files
        wav_count = len([f for f in os.listdir(bloom_wavs) if f.endswith('.wav')])
        with open(bloom_metadata, 'r') as f:
            meta_count = len(f.readlines())
        print(f"✅ Bloom dataset verified: {meta_count} metadata entries, {wav_count} audio files")
    else:
        print("❌ Bloom dataset not properly prepared")
        return False
    
    # Step 6: Analyze character set
    print("\n🔍 Analyzing Bisaya character set...")
    try:
        exec(open("analyze_bisaya_characters.py").read())
    except Exception as e:
        print(f"⚠️  Character analysis failed: {e}")
    
    # Step 7: Prepare phoneme dataset
    print("\n🔤 Preparing phoneme dataset...")
    try:
        exec(open("prepare_phoneme_dataset.py").read())
    except Exception as e:
        print(f"⚠️  Phoneme dataset preparation failed: {e}")
    
    print("\n🎉 Setup complete!")
    return True

def show_training_commands():
    """
    Display the commands to run training
    """
    print("\n📋 Training Commands:")
    print("=" * 40)
    
    print("\n1️⃣  Train Bloom LoRA (Voice characteristics):")
    print("   python train_bloom_lora.py")
    
    print("\n2️⃣  Train Phoneme LoRA (Pronunciation):")
    print("   python train_phoneme_lora.py")
    
    print("\n3️⃣  Manual training with custom parameters:")
    print("   python train_vits_lora.py \\")
    print("     --dataset_path bloom_ceb_dataset/train \\")
    print("     --description 'Custom Bloom LoRA' \\")
    print("     --r 16 \\")
    print("     --pretrained_model_path pretrained_models/ljspeech_vits_pretrained.pth")
    
    print("\n📊 Monitor training:")
    print("   - Check 'training/bisaya_vits_lora/' for outputs")
    print("   - LoRA adapters saved as timestamped .pth files")
    print("   - Training logs in lora_versions.csv")

def check_gpu():
    """
    Check GPU availability
    """
    import torch
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        print(f"🚀 GPU available: {gpu_name}")
        return True
    else:
        print("⚠️  No GPU available - training will be slow")
        return False

if __name__ == "__main__":
    # Run setup
    if setup_colab_environment():
        check_gpu()
        show_training_commands()
        
        print("\n✨ Ready to start training!")
        print("Run the training commands above to begin LoRA fine-tuning.")
    else:
        print("\n❌ Setup failed. Please check the errors above.")
