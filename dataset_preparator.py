import os
import shutil

def prepare_split(split_path):
    """
    Prepares a single dataset split (train/validation/test) in LJSpeech format.
    - Moves audio files into wavs/
    - Converts transcripts.txt -> metadata_ljspeech.csv
    """
    audio_dir = os.path.join(split_path, "audio")
    wavs_dir = os.path.join(split_path, "wavs")
    transcripts_file = os.path.join(split_path, "transcripts.txt")
    metadata_file = os.path.join(split_path, "metadata_ljspeech.csv")

    # --- Step 1: Ensure wavs/ folder ---
    os.makedirs(wavs_dir, exist_ok=True)

    # Move/copy audio files into wavs/
    if os.path.exists(audio_dir):
        for file in os.listdir(audio_dir):
            if file.endswith(".wav"):
                src = os.path.join(audio_dir, file)
                dst = os.path.join(wavs_dir, file)
                if not os.path.exists(dst):
                    shutil.copy2(src, dst)
        print(f"[OK] Copied wav files to: {wavs_dir}")
    else:
        print(f"[WARN] audio/ folder not found in {split_path}")

    # --- Step 2: Convert transcripts.txt -> metadata_ljspeech.csv ---
    if os.path.exists(transcripts_file):
        with open(transcripts_file, "r", encoding="utf-8") as f:
            lines = f.readlines()

        with open(metadata_file, "w", encoding="utf-8") as f:
            for line in lines:
                parts = line.strip().split("\t")
                if len(parts) >= 2:
                    filename = parts[0].replace(".wav", "")
                    text = parts[1]
                    f.write(f"{filename}|{text}|{text}\n")

        print(f"[OK] Created metadata_ljspeech.csv at {metadata_file}")
    else:
        print(f"[WARN] transcripts.txt not found in {split_path}")


if __name__ == "__main__":
    base_path = "/content/drive/MyDrive/Thesis_CS4A/bloom_ceb_dataset"
    splits = ["train", "validation", "test"]

    for split in splits:
        split_path = os.path.join(base_path, split)
        if os.path.exists(split_path):
            print(f"\n--- Preparing {split} split ---")
            prepare_split(split_path)
        else:
            print(f"[ERROR] Split folder not found: {split_path}")

    print("\n✅ Dataset preparation finished!")
