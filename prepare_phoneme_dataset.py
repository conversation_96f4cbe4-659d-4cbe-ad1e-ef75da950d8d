#!/usr/bin/env python3
"""
Convert Cebuano phonemic syllable data to LJSpeech format for LoRA training.
This script processes the syllable-based audio files and creates phonemic transcriptions.
"""

import os
import shutil
import pandas as pd
from pathlib import Path

def prepare_phoneme_dataset():
    """
    Convert the Cebuano phonemic TTS data to LJSpeech format
    """
    # Source paths
    source_audio_dir = "Cebuano-Phonemic-TTS-master/data/speech_database"
    
    # Output paths
    output_dir = "phoneme_dataset"
    output_wavs_dir = os.path.join(output_dir, "wavs")
    output_metadata = os.path.join(output_dir, "metadata_ljspeech.csv")
    
    # Create output directories
    os.makedirs(output_wavs_dir, exist_ok=True)
    
    # Check if source directory exists
    if not os.path.exists(source_audio_dir):
        print(f"❌ Source directory not found: {source_audio_dir}")
        print("Please ensure the Cebuano-Phonemic-TTS-master folder is in the correct location.")
        return False
    
    # Get all wav files from speech database
    wav_files = []
    for file in os.listdir(source_audio_dir):
        if file.endswith('.wav'):
            wav_files.append(file)
    
    if not wav_files:
        print(f"❌ No WAV files found in {source_audio_dir}")
        return False
    
    print(f"📁 Found {len(wav_files)} syllable audio files")
    
    # Process each file
    metadata_lines = []
    copied_count = 0
    
    for wav_file in sorted(wav_files):
        # Extract syllable from filename (remove .wav extension)
        syllable = wav_file.replace('.wav', '')
        
        # Create phonemic transcription (syllable itself is the phoneme)
        # For Cebuano, the syllable names are already phonemic representations
        phonemic_text = syllable
        
        # Copy audio file to output directory
        src_path = os.path.join(source_audio_dir, wav_file)
        dst_path = os.path.join(output_wavs_dir, wav_file)
        
        try:
            shutil.copy2(src_path, dst_path)
            
            # Create metadata entry (filename without .wav | text | normalized_text)
            filename_no_ext = wav_file.replace('.wav', '')
            metadata_lines.append(f"{filename_no_ext}|{phonemic_text}|{phonemic_text}")
            copied_count += 1
            
        except Exception as e:
            print(f"⚠️  Failed to copy {wav_file}: {e}")
    
    # Write metadata file
    with open(output_metadata, 'w', encoding='utf-8') as f:
        f.write('\n'.join(metadata_lines))
    
    print(f"✅ Successfully prepared phoneme dataset:")
    print(f"   📂 Audio files: {copied_count} files in {output_wavs_dir}")
    print(f"   📄 Metadata: {output_metadata}")
    print(f"   📊 Total syllables: {len(metadata_lines)}")
    
    # Show sample entries
    print(f"\n📋 Sample metadata entries:")
    for i, line in enumerate(metadata_lines[:5]):
        print(f"   {i+1}. {line}")
    if len(metadata_lines) > 5:
        print(f"   ... and {len(metadata_lines) - 5} more")
    
    return True

def create_combined_dataset():
    """
    Create a combined dataset with both Bloom and phoneme data
    """
    print("\n🔄 Creating combined dataset...")
    
    # Paths
    bloom_metadata = "bloom_ceb_dataset/train/metadata_ljspeech.csv"
    phoneme_metadata = "phoneme_dataset/metadata_ljspeech.csv"
    combined_dir = "combined_dataset"
    combined_wavs = os.path.join(combined_dir, "wavs")
    combined_metadata = os.path.join(combined_dir, "metadata_ljspeech.csv")
    
    # Create combined directory
    os.makedirs(combined_wavs, exist_ok=True)
    
    combined_lines = []
    
    # Add Bloom data
    if os.path.exists(bloom_metadata):
        with open(bloom_metadata, 'r', encoding='utf-8') as f:
            bloom_lines = f.readlines()
        
        # Copy Bloom audio files
        bloom_wavs_dir = "bloom_ceb_dataset/train/wavs"
        for line in bloom_lines:
            filename = line.split('|')[0]
            src_wav = os.path.join(bloom_wavs_dir, f"{filename}.wav")
            dst_wav = os.path.join(combined_wavs, f"bloom_{filename}.wav")
            
            if os.path.exists(src_wav):
                shutil.copy2(src_wav, dst_wav)
                # Update metadata with new filename
                parts = line.strip().split('|')
                new_line = f"bloom_{parts[0]}|{parts[1]}|{parts[2]}"
                combined_lines.append(new_line)
        
        print(f"   ✅ Added {len(bloom_lines)} Bloom samples")
    
    # Add phoneme data
    if os.path.exists(phoneme_metadata):
        with open(phoneme_metadata, 'r', encoding='utf-8') as f:
            phoneme_lines = f.readlines()
        
        # Copy phoneme audio files
        phoneme_wavs_dir = "phoneme_dataset/wavs"
        for line in phoneme_lines:
            filename = line.split('|')[0]
            src_wav = os.path.join(phoneme_wavs_dir, f"{filename}.wav")
            dst_wav = os.path.join(combined_wavs, f"phoneme_{filename}.wav")
            
            if os.path.exists(src_wav):
                shutil.copy2(src_wav, dst_wav)
                # Update metadata with new filename
                parts = line.strip().split('|')
                new_line = f"phoneme_{parts[0]}|{parts[1]}|{parts[2]}"
                combined_lines.append(new_line)
        
        print(f"   ✅ Added {len(phoneme_lines)} phoneme samples")
    
    # Write combined metadata
    with open(combined_metadata, 'w', encoding='utf-8') as f:
        f.write('\n'.join(combined_lines))
    
    print(f"✅ Combined dataset created:")
    print(f"   📂 Location: {combined_dir}")
    print(f"   📊 Total samples: {len(combined_lines)}")
    
    return True

if __name__ == "__main__":
    print("🚀 Preparing Cebuano Phoneme Dataset for LoRA Training")
    print("=" * 60)
    
    # Step 1: Prepare phoneme dataset
    if prepare_phoneme_dataset():
        print("\n" + "=" * 60)
        
        # Step 2: Create combined dataset (optional)
        create_combined_dataset()
        
        print("\n🎉 Dataset preparation complete!")
        print("\n📋 Next steps:")
        print("1. Use 'phoneme_dataset' for phoneme-focused LoRA training")
        print("2. Use 'bloom_ceb_dataset/train' for voice-focused LoRA training") 
        print("3. Use 'combined_dataset' for joint training (experimental)")
        
    else:
        print("\n❌ Dataset preparation failed!")
