# WORKING REQUIREMENTS FOR PYTHON 3.12 + TTS
# Based on your previous working setup but fixed for compatibility

# Core ML libraries (install these first)
torch
torchaudio
numpy
pandas
matplotlib
scipy

# Audio processing
librosa
soundfile
audioread

# Text processing (essential for TTS)
inflect
unidecode
anyascii
pysbd

# TTS training utilities
coqpit
trainer
tqdm

# Additional utilities
ipython
ipywidgets
tensorboard

# Language processing
phonemizer
gruut
gruut-ipa

# TTS language support
bangla
bnnumerizer
bnunicodenormalizer
g2pkk
hangul-romanize
jamo
num2words
pypinyin

# Development tools
black
coverage