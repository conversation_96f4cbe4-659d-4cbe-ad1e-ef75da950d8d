# Pure PyTorch Requirements for Bisaya VITS LoRA Training
# No TTS library dependencies - works with Python 3.12+

# Core ML libraries
torch>=2.0.0
torchaudio>=2.0.0

# Audio processing
librosa>=0.10.0
soundfile>=0.12.0

# Data handling
numpy>=1.24.0
pandas>=1.5.0

# Visualization and utilities
matplotlib>=3.6.0
tqdm>=4.64.0

# Additional utilities for Colab
ipython
ipywidgets

# Optional: For advanced audio processing
scipy>=1.10.0
