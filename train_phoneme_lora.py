
import os
# Updated paths to match your actual structure
# Note: Run prepare_phoneme_dataset.py first to create the phoneme dataset
# Reduced epochs for Colab compatibility (20 epochs = ~2-3 hours)
os.system("python train_vits_lora.py --dataset_path '/content/drive/MyDrive/Thesis_CS4A/phoneme_dataset' --description 'Phoneme LoRA' --r 8 --epochs 20 --pretrained_model_path '/content/drive/MyDrive/Thesis_CS4A/pretrained_models/ljspeech_vits_pretrained.pth'")
