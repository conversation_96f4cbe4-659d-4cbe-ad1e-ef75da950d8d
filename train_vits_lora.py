
import os
import argparse
import torch
import torch.nn as nn
import shutil
import pandas as pd
from datetime import datetime
from TTS.tts.configs.shared_configs import BaseDatasetConfig
from TTS.tts.configs.vits_config import VitsConfig
from TTS.tts.datasets import load_tts_samples
from TTS.tts.models.vits import Vits, VitsAudioConfig
from TTS.tts.utils.text.tokenizer import TTSTokenizer
from TTS.utils.audio import AudioProcessor
from trainer import Trainer, TrainerArgs
from TTS.tts.models.vits import CharactersConfig

# === LoRA Module ===
class LoRALinearConv1D(nn.Module):
    def __init__(self, original_conv, r=4, alpha=1.0):
        super().__init__()
        self.original = original_conv
        self.r = r
        self.alpha = alpha
        self.lora_A = nn.Conv1d(original_conv.in_channels, r, kernel_size=1, bias=False)
        self.lora_B = nn.Conv1d(r, original_conv.out_channels, kernel_size=1, bias=False)
        self.scaling = self.alpha / self.r
        for param in self.original.parameters():
            param.requires_grad = False

    def forward(self, x):
        return self.original(x) + self.scaling * self.lora_B(self.lora_A(x))

# === Inject LoRA into Attention ===
def inject_lora_into_vits(model, r=4):
    for name, module in model.named_modules():
        if \"RelativePositionMultiHeadAttention\" in str(type(module)):
            print(f\"Injecting LoRA into: {name}\")
            module.conv_q = LoRALinearConv1D(module.conv_q, r)
            module.conv_k = LoRALinearConv1D(module.conv_k, r)
            module.conv_v = LoRALinearConv1D(module.conv_v, r)

# === Extract and Save LoRA Parameters ===
def extract_lora_parameters(model):
    lora_state = {}
    for name, module in model.named_modules():
        if isinstance(module, LoRALinearConv1D):
            lora_state[f\"{name}.lora_A.weight\"] = module.lora_A.weight.data.cpu()
            lora_state[f\"{name}.lora_B.weight\"] = module.lora_B.weight.data.cpu()
    return lora_state

# === Log LoRA Metadata ===
def log_lora_metadata(log_path, filename, description=\"\"):
    log_file = os.path.join(log_path, \"lora_versions.csv\")
    timestamp = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")
    log_entry = pd.DataFrame([[timestamp, filename, description]], columns=[\"timestamp\", \"filename\", \"description\"])
    if os.path.exists(log_file):
        log_entry.to_csv(log_file, mode='a', index=False, header=False)
    else:
        log_entry.to_csv(log_file, index=False)

# === GPU Info ===
def setup_gpu():
    if not torch.cuda.is_available():
        print(\"\\nNo GPU detected! Training will be on CPU.\\n\")
        return False
    device = torch.cuda.get_device_name(0)
    print(f\"\\nGPU detected: {device}\")
    return True

# === Dataset Setup ===
def setup_dataset(dataset_path):
    wavs_dir = os.path.join(dataset_path, \"wavs\")
    os.makedirs(wavs_dir, exist_ok=True)
    audio_dir = os.path.join(dataset_path, \"audio\")
    # if dataset uses 'audio' folder copy files to 'wavs'
    if os.path.exists(audio_dir):
        for file in os.listdir(audio_dir):
            if file.endswith(\".wav\"):
                shutil.copy2(os.path.join(audio_dir, file), os.path.join(wavs_dir, file))
    return wavs_dir

def convert_transcripts_to_ljspeech_format(dataset_path):
    transcripts_path = os.path.join(dataset_path, \"transcripts.txt\")
    output_path = os.path.join(dataset_path, \"metadata_ljspeech.csv\")
    if not os.path.exists(transcripts_path):
        # assume metadata already exists
        return output_path if os.path.exists(output_path) else None
    with open(transcripts_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    with open(output_path, 'w', encoding='utf-8') as f:
        for line in lines:
            parts = line.strip().split('\\t')
            if len(parts) >= 2:
                filename = parts[0].replace('.wav', '')
                transcription = parts[1]
                f.write(f\"{filename}|{transcription}|{transcription}\\n\")
    print(f\"Converted transcripts to LJSpeech format: {output_path}\")
    return output_path

def main():
    has_gpu = setup_gpu()
    parser = argparse.ArgumentParser()
    parser.add_argument('--continue_path', type=str)
    parser.add_argument('--pretrained_model_path', type=str)
    parser.add_argument('--dataset_path', type=str, required=True, help='Path to LJSpeech-format dataset')
    parser.add_argument('--description', type=str, default='', help='Description for this LoRA run')
    parser.add_argument('--r', type=int, default=8, help='LoRA rank')
    parser.add_argument('--epochs', type=int, default=50, help='Number of training epochs')
    args = parser.parse_args()

    output_path = args.continue_path or os.path.join(os.path.dirname(os.path.abspath(__file__)), \"training/bisaya_vits_lora\")
    dataset_path = args.dataset_path
    phoneme_cache_path = os.path.join(output_path, \"phoneme_cache\")
    os.makedirs(output_path, exist_ok=True)
    os.makedirs(phoneme_cache_path, exist_ok=True)

    setup_dataset(dataset_path)
    meta = convert_transcripts_to_ljspeech_format(dataset_path)
    if meta is None:
        print(\"No transcripts found and no metadata_ljspeech.csv present. Exiting.\")
        return

    dataset_config = BaseDatasetConfig(formatter=\"ljspeech\", meta_file_train=\"metadata_ljspeech.csv\", path=dataset_path)
    audio_config = VitsAudioConfig(sample_rate=22050, win_length=1024, hop_length=256, num_mels=80, mel_fmin=0, mel_fmax=None)

    config = VitsConfig(
        audio=audio_config,
        run_name=\"bisaya_vits_lora\",
        run_description=f\"LoRA training: {args.description}\",
        batch_size=32 if has_gpu else 4,
        eval_batch_size=16 if has_gpu else 2,
        batch_group_size=8 if has_gpu else 3,
        num_loader_workers=8 if has_gpu else 2,
        num_eval_loader_workers=4 if has_gpu else 1,
        run_eval=True,
        test_delay_epochs=5,
        epochs=args.epochs,
        characters=CharactersConfig(
            characters_class=\"TTS.tts.models.vits.VitsCharacters\",
            pad=\"_\", eos=\"\", bos=\"\",
            characters=\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz\",
            punctuations=\";:,.!?¡¿—…\\\"«»\\\"\\\" \",
            phonemes=\"ɑɐɒæɓʙβɔɕçɗɖðʤəɘɚɛɜɝɞɟʄɡɠɢʛɦɧħɥʜɨɪʝɭɬɫɮʟɱɯɰŋɳɲɴøɵɸθœɶʘɹɺɾɻʀʁɽʂʃʈʧʉʊʋⱱʌɣɤʍχʎʏʑʐʒʔʡʕʢǀǁǂǃˈˌːˑʼʴʰʱʲʷˠˤ˞↓↑→↗↘'̩'ᵻ\"
        ),
        text_cleaner=\"phoneme_cleaners\",
        phoneme_language=\"en\",
        use_phonemes=True,
        phoneme_cache_path=phoneme_cache_path,
        compute_input_seq_cache=True,
        print_step=5,
        print_eval=True,
        mixed_precision=False,
        output_path=output_path,
        datasets=[dataset_config],
        cudnn_benchmark=has_gpu,
        eval_split_size=0.02,
        eval_split_max_size=2,
        test_sentences=[[\"Kumusta ka\"], [\"Maayong buntag\"], [\"Ang tubig sa suba kay klaro kaayo\"], [\"Gusto ko moadto sa dagat\"]]
    )

    ap = AudioProcessor.init_from_config(config)
    tokenizer, config = TTSTokenizer.init_from_config(config)
    train_samples, eval_samples = load_tts_samples(dataset_config, eval_split=True, eval_split_max_size=config.eval_split_max_size, eval_split_size=config.eval_split_size)

    model = Vits(config, ap, tokenizer, speaker_manager=None)
    if args.pretrained_model_path:
        try:
            checkpoint = torch.load(args.pretrained_model_path, map_location='cpu')
            model.load_state_dict(checkpoint['model'], strict=False)
            print(\"Pre-trained model loaded successfully!\")
        except Exception as e:
            print(f\"Failed to load checkpoint: {e}\")

    inject_lora_into_vits(model, r=args.r)
    for name, param in model.named_parameters():
        if \"lora\" not in name:
            param.requires_grad = False

    if has_gpu:
        model = model.cuda()

    trainer_args = TrainerArgs()
    trainer_args.mixed_precision = False
    trainer_args.num_loader_workers = 8 if has_gpu else 2
    trainer_args.num_eval_loader_workers = 4 if has_gpu else 1
    trainer_args.batch_size = 32 if has_gpu else 4
    trainer_args.eval_batch_size = 16 if has_gpu else 2
    trainer_args.batch_group_size = 8 if has_gpu else 3

    if args.continue_path:
        trainer_args.continue_path = args.continue_path

    trainer = Trainer(trainer_args, config, output_path, model=model, train_samples=train_samples, eval_samples=eval_samples)
    trainer.fit()

    # Save LoRA weights with timestamped filename and log metadata
    lora_state_dict = extract_lora_parameters(model)
    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")
    lora_filename = f\"lora_adapters_{timestamp}.pth\"
    lora_save_path = os.path.join(output_path, lora_filename)
    torch.save(lora_state_dict, lora_save_path)
    print(f\"\\n✅ LoRA adapter weights saved to {lora_save_path}\")

    # Log the saved adapter
    val_loss = trainer.best_loss.get(\"eval_loss\", float('nan'))
    train_loss = trainer.best_loss.get(\"train_loss\", float('nan'))
    epoch = getattr(trainer, 'epoch', 'N/A')
    log_lora_metadata(
        output_path,
        lora_filename,
        description=f\"Epoch={epoch}, train_loss={train_loss:.4f}, val_loss={val_loss:.4f}, desc={args.description}\"
    )

if __name__ == \"__main__\":
    import multiprocessing
    multiprocessing.freeze_support()
    main()
